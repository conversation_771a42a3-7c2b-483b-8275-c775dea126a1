# Task 2 - ifExample script
# This script demonstrates if and if-else statements with folder operations

# First, create a new_folder so we can test our if statement
Write-Output "Creating new_folder for testing..."
New-Item -Path "new_folder" -ItemType Directory

# If statement: Create if_folder IF new_folder already exists
Write-Output "Checking if new_folder exists..."
if (Test-Path -Path "new_folder") {
    Write-Output "new_folder exists, so creating if_folder..."
    New-Item -Path "if_folder" -ItemType Directory
    Write-Output "if_folder has been created"
}

# If-else statement: Check if if_folder exists
Write-Output "Now checking if if_folder exists..."
if (Test-Path -Path "if_folder") {
    # If if_folder exists, create hyperionDev folder
    Write-Output "if_folder exists, so creating hyperionDev folder..."
    New-Item -Path "hyperionDev" -ItemType Directory
    Write-Output "hyperionDev folder has been created"
}
else {
    # If if_folder doesn't exist, create new-projects folder
    Write-Output "if_folder does not exist, so creating new-projects folder..."
    New-Item -Path "new-projects" -ItemType Directory
    Write-Output "new-projects folder has been created"
}

Write-Output "ifExample script completed!"
