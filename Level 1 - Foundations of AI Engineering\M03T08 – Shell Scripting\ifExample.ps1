# my ifExample script - testing if statements

# making a folder first so i can test the if thing
Write-Output "making new_folder..."
New-Item -Path "new_folder" -ItemType Directory

# if statement - make if_folder only if new_folder exists
Write-Output "checking if new_folder is there..."
if (Test-Path -Path "new_folder") {
    Write-Output "yep new_folder exists, making if_folder now..."
    New-Item -Path "if_folder" -ItemType Directory
    Write-Output "made if_folder"
}

# if-else statement - checking if if_folder exists
Write-Output "now checking if if_folder exists..."
if (Test-Path -Path "if_folder") {
    # if if_folder exists, make hyperionDev folder
    Write-Output "if_folder is there, so making hyperionDev folder..."
    New-Item -Path "hyperionDev" -ItemType Directory
    Write-Output "made hyperionDev folder"
}
else {
    # if if_folder doesnt exist, make new-projects folder
    Write-Output "if_folder not there, making new-projects folder instead..."
    New-Item -Path "new-projects" -ItemType Directory
    Write-Output "made new-projects folder"
}

Write-Output "all done with this one!"
