$menu = Read-Host "Select an option by entering a number :`n1 - list directories and files in the current directory`n2 - Create a new file in the current directory'n3 - Creat a new folderin the current diractory.`n" if ( $menu -eq 1){ ls } elseif ($menu -eq 2){ $file = Read-Host "Enter the name of the file that you want to create" if (Test-Path -Path $file){Write-Output "The file named $file already exists"} else{New-Item $file Write-Output "File with the name $file has been created"} } elseif ($menu -eq 3){ $folder = Read-Host "Enter the name of the folder that you want to create" if (Test-Path -Path $folder){Write-Output "THe folder named $folder already exists."} else {New-Item -path $folder -ItemType Directory Write_Output "Folder with the name $folder has been created."}} else {Write-Output "You Have made an invalid choice."}