# Task 1 - file_cd script
# This script creates folders, navigates into them, and removes some folders

# Create three new folders
Write-Output "Creating three main folders..."
New-Item -Path "my_documents" -ItemType Directory
New-Item -Path "my_photos" -ItemType Directory
New-Item -Path "my_projects" -ItemType Directory
Write-Output "Created: my_documents, my_photos, my_projects"

# Navigate inside one of the folders (my_projects)
Write-Output "Going into my_projects folder..."
Set-Location "my_projects"

# Create three new folders inside this folder
Write-Output "Creating three folders inside my_projects..."
New-Item -Path "web_stuff" -ItemType Directory
New-Item -Path "school_work" -ItemType Directory
New-Item -Path "random_ideas" -ItemType Directory
Write-Output "Created: web_stuff, school_work, random_ideas"

# Remove two of the folders we created
Write-Output "Removing two folders..."
Remove-Item "random_ideas"
Remove-Item "web_stuff"
Write-Output "Removed: random_ideas and web_stuff"

# Go back to the main directory
Set-Location ".."
Write-Output "Back to main directory. Task completed!"