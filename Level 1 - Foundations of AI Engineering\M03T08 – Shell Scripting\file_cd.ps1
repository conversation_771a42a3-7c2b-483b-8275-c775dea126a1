# My file and folder menu script
$menu = Read-Host "Hey! Pick a number for what you want to do:"`n1 - Show me what files and folders are here`n2 - Make a new file`n"

while($menu -ne 4){
	if ($menu -eq 1){
		# show whats in the folders
		ls
	}
    elseif ($menu -eq 2){
        # Get the file name and make the file
        $file = Read-Host "What do you want to call the new file?" 
        # Check if the file already exists
        if(Test-Path -Path $file){
            Write-Output "That file name already exists"
		}
        else{
            New-Iten $file 
            Write-Output "File called $file has been made "
        }
	}
    elseif ($menu -eq 3){
       # Get the folder name and make the folder
        $folder = Read-Host "What do you want to call the new folder?"
        # Check if the folder already exists
        if(Test-path -Path $folder){
           	Write_output "That folder name already exists"
        }
        else{
           # Make a new folder
         	New-Item -Path $folder -ItemType Directory
            WriteOutput "Folder called sfolder has been made"
    	}	
	}
	else{
		Write-Output "that's not a valid choice, try again"
	}
	# Showing the menu again
	$menu = Read-Host "Pick a number for what you want to do:`n1 - Show me what files and folders are here`n2 - Make a new file`n3 - Make a new folder`n4 - EXIT`n"
}
Write-Output "Muchas gracias for using my script!"