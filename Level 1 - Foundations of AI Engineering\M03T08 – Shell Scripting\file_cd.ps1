# my file_cd script - making folders and stuff

# making 3 folders first
Write-Output "making some folders..."
New-Item -Path "my_stuff" -ItemType Directory
New-Item -Path "random_things" -ItemType Directory
New-Item -Path "school_projects" -ItemType Directory
Write-Output "ok made 3 folders"

# going into one of them
Write-Output "going into school_projects..."
Set-Location "school_projects"

# making 3 more folders inside this one
Write-Output "making more folders inside here..."
New-Item -Path "homework" -ItemType Directory
New-Item -Path "assignments" -ItemType Directory
New-Item -Path "notes" -ItemType Directory
Write-Output "made 3 more folders"

# deleting 2 of them
Write-Output "removing some folders now..."
Remove-Item "notes"
Remove-Item "assignments"
Write-Output "deleted 2 folders"

# going back
Set-Location ".."
Write-Output "done with this script!"