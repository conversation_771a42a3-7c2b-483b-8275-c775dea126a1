# My Menu <PERSON>t - Student Version
# This script creates a simple menu for file and folder operations

# Display the menu and get user choice
$menu = Read-Host "Select an option by entering a number:
1 - List directories and files in the current directory
2 - Create a new file in the current directory
3 - Create a new folder in the current directory
4 - Exit
"

# Keep showing the menu until user chooses to exit
while ($menu -ne 4) {
    
    if ($menu -eq 1) {
        # Option 1: List all files and folders
        Write-Output "Files and folders in current directory:"
        ls
    }
    elseif ($menu -eq 2) {
        # Option 2: Create a new file
        $file = Read-Host "Enter the name of the file that you want to create"
        
        # Check if file already exists
        if (Test-Path -Path $file) {
            Write-Output "The file named $file already exists"
        }
        else {
            # Create the file
            New-Item $file
            Write-Output "File with the name $file has been created"
        }
    }
    elseif ($menu -eq 3) {
        # Option 3: Create a new folder
        $folder = Read-Host "Enter the name of the folder that you want to create"
        
        # Check if folder already exists
        if (Test-Path -Path $folder) {
            Write-Output "The folder named $folder already exists"
        }
        else {
            # Create the folder
            New-Item -Path $folder -ItemType Directory
            Write-Output "Folder with the name $folder has been created"
        }
    }
    else {
        # Invalid choice
        Write-Output "You have made an invalid choice."
    }
    
    # Show the menu again
    $menu = Read-Host "Select an option by entering a number:
1 - List directories and files in the current directory
2 - Create a new file in the current directory
3 - Create a new folder in the current directory
4 - Exit
"
}

Write-Output "Thank you for using the menu script!"
